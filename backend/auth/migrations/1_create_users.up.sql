CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  phone_number VA<PERSON><PERSON>R(20) UNIQUE NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  email VARCHAR(255),
  role VARCHAR(50) NOT NULL DEFAULT 'customer',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE TABLE otp_codes (
  id BIGSERIAL PRIMARY KEY,
  phone_number VARCHAR(20) NOT NULL,
  code VARCHAR(6) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_used BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_otp_codes_phone_number ON otp_codes(phone_number);
CREATE INDEX idx_otp_codes_expires_at ON otp_codes(expires_at);

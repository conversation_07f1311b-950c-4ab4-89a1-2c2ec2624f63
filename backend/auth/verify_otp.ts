import { api, APIError } from "encore.dev/api";
import { authDB } from "./db";

interface VerifyOTPRequest {
  phoneNumber: string;
  otpCode: string;
}

export interface VerifyOTPResponse {
  success: boolean;
  user: {
    id: number;
    phoneNumber: string;
    name: string | null;
    email: string | null;
    role: string;
  };
  token: string;
}

// Verifies the OTP code and returns user information.
export const verifyOTP = api<VerifyOTPRequest, VerifyOTPResponse>(
  { expose: true, method: "POST", path: "/auth/verify-otp" },
  async (req) => {
    const { phoneNumber, otpCode } = req;

    // Check if OTP is valid and not expired
    const otpRecord = await authDB.queryRow`
      SELECT * FROM otp_codes
      WHERE phone_number = ${phoneNumber}
        AND code = ${otpCode}
        AND expires_at > NOW()
        AND is_used = false
      ORDER BY created_at DESC
      LIMIT 1
    `;

    if (!otpRecord) {
      throw APIError.invalidArgument("Invalid or expired OTP");
    }

    // Mark OTP as used
    await authDB.exec`
      UPDATE otp_codes
      SET is_used = true
      WHERE id = ${otpRecord.id}
    `;

    // Check if user exists, create if not
    let user = await authDB.queryRow`
      SELECT * FROM users WHERE phone_number = ${phoneNumber}
    `;

    if (!user) {
      await authDB.exec`
        INSERT INTO users (phone_number, role)
        VALUES (${phoneNumber}, 'customer')
      `;

      user = await authDB.queryRow`
        SELECT * FROM users WHERE phone_number = ${phoneNumber}
      `;
    }

    if (!user) {
      throw APIError.internal("Failed to create user");
    }

    // Generate simple token (in production, use JWT or similar)
    const token = `token_${user.id}_${Date.now()}`;

    return {
      success: true,
      user: {
        id: user.id,
        phoneNumber: user.phone_number,
        name: user.name,
        email: user.email,
        role: user.role
      },
      token
    };
  }
);

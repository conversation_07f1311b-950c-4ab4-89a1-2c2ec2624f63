import { api, APIError } from "encore.dev/api";
import { authDB } from "./db";

interface SendOTPRequest {
  phoneNumber: string;
}

interface SendOTPResponse {
  success: boolean;
  message: string;
}

// Sends an OTP code to the provided phone number.
export const sendOTP = api<SendOTPRequest, SendOTPResponse>(
  { expose: true, method: "POST", path: "/auth/send-otp" },
  async (req) => {
    const { phoneNumber } = req;

    if (!phoneNumber || phoneNumber.length < 10) {
      throw APIError.invalidArgument("Invalid phone number");
    }

    // Generate 6-digit OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store OTP in database
    await authDB.exec`
      INSERT INTO otp_codes (phone_number, code, expires_at)
      VALUES (${phoneNumber}, ${otpCode}, ${expiresAt})
    `;

    // TODO: Integrate with SMS service to send actual OTP
    console.log(`OTP for ${phoneNumber}: ${otpCode}`);

    return {
      success: true,
      message: "OTP sent successfully"
    };
  }
);

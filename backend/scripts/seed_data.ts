import { authDB } from "../auth/db";
import { vendorsDB } from "../vendors/db";
import { catalogDB } from "../catalog/db";
import { productsDB } from "../products/db";

async function seedData() {
  console.log("🌱 Starting database seeding...");

  try {
    // 1. Seed Users
    console.log("👥 Seeding users...");
    await authDB.exec`
      INSERT INTO users (phone_number, name, email, role) VALUES
      ('+1234567890', '<PERSON>', '<EMAIL>', 'vendor'),
      ('+1234567891', '<PERSON>', '<EMAIL>', 'vendor'),
      ('+1234567892', '<PERSON>', '<EMAIL>', 'vendor'),
      ('+1234567893', '<PERSON>', '<EMAIL>', 'vendor'),
      ('+1234567894', '<PERSON>', '<EMAIL>', 'customer'),
      ('+1234567895', '<PERSON>', '<EMAIL>', 'customer'),
      ('+1234567896', '<PERSON>', '<EMAIL>', 'customer'),
      ('+1234567897', '<PERSON>', '<EMAIL>', 'customer')
      ON CONFLICT (phone_number) DO NOTHING
    `;

    // 2. Seed Categories
    console.log("📂 Seeding categories...");
    await catalogDB.exec`
      INSERT INTO categories (name, description, icon) VALUES
      ('Electronics', 'Electronic devices and gadgets', '📱'),
      ('Groceries', 'Food and daily essentials', '🛒'),
      ('Clothing', 'Apparel and fashion items', '👕'),
      ('Books', 'Books and educational materials', '📚'),
      ('Home & Garden', 'Home improvement and gardening supplies', '🏠'),
      ('Health & Beauty', 'Health and beauty products', '💄'),
      ('Sports & Outdoors', 'Sports equipment and outdoor gear', '⚽'),
      ('Toys & Games', 'Toys and games for all ages', '🎮')
      ON CONFLICT DO NOTHING
    `;

    // 3. Seed Subcategories
    console.log("📁 Seeding subcategories...");
    await catalogDB.exec`
      INSERT INTO subcategories (category_id, name, description) VALUES
      (1, 'Smartphones', 'Mobile phones and accessories'),
      (1, 'Laptops', 'Portable computers'),
      (1, 'Headphones', 'Audio devices'),
      (1, 'Tablets', 'Tablet computers'),
      (2, 'Fruits & Vegetables', 'Fresh produce'),
      (2, 'Dairy Products', 'Milk, cheese, yogurt'),
      (2, 'Snacks', 'Packaged snacks and treats'),
      (2, 'Beverages', 'Drinks and beverages'),
      (3, 'Men''s Clothing', 'Clothing for men'),
      (3, 'Women''s Clothing', 'Clothing for women'),
      (3, 'Shoes', 'Footwear for all'),
      (3, 'Accessories', 'Fashion accessories'),
      (4, 'Fiction', 'Novels and stories'),
      (4, 'Non-Fiction', 'Educational and reference books'),
      (4, 'Children''s Books', 'Books for children'),
      (5, 'Furniture', 'Home furniture'),
      (5, 'Garden Tools', 'Gardening equipment'),
      (5, 'Decor', 'Home decoration items'),
      (6, 'Skincare', 'Skin care products'),
      (6, 'Makeup', 'Cosmetics and beauty products'),
      (7, 'Fitness Equipment', 'Exercise and fitness gear'),
      (7, 'Outdoor Gear', 'Camping and hiking equipment'),
      (8, 'Board Games', 'Traditional board games'),
      (8, 'Video Games', 'Electronic games')
      ON CONFLICT DO NOTHING
    `;

    // 4. Seed Vendors
    console.log("🏪 Seeding vendors...");
    await vendorsDB.exec`
      INSERT INTO vendors (user_id, business_name, owner_name, phone_number, email, address, description, is_verified) VALUES
      (1, 'Tech World', 'John Smith', '+1234567890', '<EMAIL>', '123 Main St, Tech City, TC 12345', 'Your one-stop shop for all electronic needs', true),
      (2, 'Fresh Market', 'Sarah Johnson', '+1234567891', '<EMAIL>', '456 Market Ave, Green Valley, GV 67890', 'Fresh groceries and organic produce', true),
      (3, 'Fashion Hub', 'Mike Wilson', '+1234567892', '<EMAIL>', '789 Style Blvd, Fashion District, FD 11111', 'Trendy clothing for all ages', true),
      (4, 'Book Corner', 'Emily Davis', '+1234567893', '<EMAIL>', '321 Library St, Knowledge Town, KT 22222', 'Wide selection of books and educational materials', true)
      ON CONFLICT DO NOTHING
    `;

    // 5. Seed Stores
    console.log("🏬 Seeding stores...");
    await vendorsDB.exec`
      INSERT INTO stores (vendor_id, name, description, address, phone_number, operating_hours) VALUES
      (1, 'Tech World Main Store', 'Main electronics store with latest gadgets', '123 Main St, Tech City, TC 12345', '+1234567890', '{"monday": "9:00-21:00", "tuesday": "9:00-21:00", "wednesday": "9:00-21:00", "thursday": "9:00-21:00", "friday": "9:00-22:00", "saturday": "9:00-22:00", "sunday": "10:00-20:00"}'),
      (2, 'Fresh Market Downtown', 'Downtown grocery store with organic options', '456 Market Ave, Green Valley, GV 67890', '+1234567891', '{"monday": "7:00-22:00", "tuesday": "7:00-22:00", "wednesday": "7:00-22:00", "thursday": "7:00-22:00", "friday": "7:00-23:00", "saturday": "7:00-23:00", "sunday": "8:00-21:00"}'),
      (3, 'Fashion Hub Outlet', 'Fashion outlet store with latest trends', '789 Style Blvd, Fashion District, FD 11111', '+1234567892', '{"monday": "10:00-20:00", "tuesday": "10:00-20:00", "wednesday": "10:00-20:00", "thursday": "10:00-20:00", "friday": "10:00-21:00", "saturday": "10:00-21:00", "sunday": "11:00-19:00"}'),
      (4, 'Book Corner Library', 'Cozy bookstore with reading nooks', '321 Library St, Knowledge Town, KT 22222', '+1234567893', '{"monday": "9:00-19:00", "tuesday": "9:00-19:00", "wednesday": "9:00-19:00", "thursday": "9:00-19:00", "friday": "9:00-20:00", "saturday": "9:00-20:00", "sunday": "10:00-18:00"}')
      ON CONFLICT DO NOTHING
    `;

    // 6. Seed Products
    console.log("📦 Seeding products...");
    await productsDB.exec`
      INSERT INTO products (store_id, category_id, subcategory_id, name, description, base_price, sku, attributes, images) VALUES
      -- Tech World Products
      (1, 1, 1, 'iPhone 15 Pro', 'Latest Apple smartphone with advanced camera system and A17 Pro chip', 999.99, 'IP15P-128', '{"color": "Space Black", "storage": "128GB", "warranty": "1 year", "brand": "Apple"}', '["https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400"]'),
      (1, 1, 1, 'Samsung Galaxy S24', 'Premium Android smartphone with AI features', 899.99, 'SGS24-256', '{"color": "Phantom Black", "storage": "256GB", "warranty": "2 years", "brand": "Samsung"}', '["https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400"]'),
      (1, 1, 2, 'MacBook Air M3', 'Lightweight laptop with M3 chip and all-day battery', 1199.99, 'MBA-M3-256', '{"color": "Silver", "storage": "256GB SSD", "ram": "8GB", "warranty": "1 year", "brand": "Apple"}', '["https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400"]'),
      (1, 1, 2, 'Dell XPS 13', 'Ultra-portable Windows laptop with stunning display', 999.99, 'DELL-XPS13', '{"color": "Platinum Silver", "storage": "512GB SSD", "ram": "16GB", "warranty": "1 year", "brand": "Dell"}', '["https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400"]'),
      (1, 1, 3, 'AirPods Pro 2', 'Wireless earbuds with active noise cancellation', 249.99, 'APP2-WHITE', '{"color": "White", "battery_life": "6 hours", "noise_cancellation": true, "brand": "Apple"}', '["https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400"]'),
      (1, 1, 3, 'Sony WH-1000XM5', 'Premium over-ear headphones with industry-leading noise cancellation', 399.99, 'SONY-WH1000XM5', '{"color": "Black", "battery_life": "30 hours", "noise_cancellation": true, "brand": "Sony"}', '["https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400"]'),

      -- Fresh Market Products
      (2, 2, 5, 'Organic Bananas', 'Fresh organic bananas from Ecuador', 2.99, 'ORG-BAN-1LB', '{"organic": true, "weight": "1 lb", "origin": "Ecuador", "shelf_life": "5-7 days"}', '["https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400"]'),
      (2, 2, 5, 'Fresh Strawberries', 'Sweet and juicy strawberries', 4.99, 'STRAW-1LB', '{"organic": false, "weight": "1 lb", "origin": "California", "shelf_life": "3-5 days"}', '["https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=400"]'),
      (2, 2, 6, 'Whole Milk', 'Fresh whole milk from local farms', 3.49, 'MILK-WHOLE-1GAL', '{"fat_content": "3.25%", "volume": "1 gallon", "expiry_days": 7, "organic": false}', '["https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400"]'),
      (2, 2, 6, 'Greek Yogurt', 'Creamy Greek yogurt with probiotics', 5.99, 'YOGURT-GREEK-32OZ', '{"fat_content": "2%", "volume": "32 oz", "flavor": "Plain", "probiotics": true}', '["https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400"]'),
      (2, 2, 7, 'Potato Chips', 'Crispy kettle-cooked potato chips', 3.99, 'CHIPS-KETTLE-150G', '{"flavor": "Sea Salt", "weight": "150g", "gluten_free": true, "organic": false}', '["https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400"]'),
      (2, 2, 8, 'Orange Juice', 'Fresh squeezed orange juice', 4.49, 'OJ-FRESH-64OZ', '{"volume": "64 oz", "pulp": "Some pulp", "vitamin_c": "100% DV", "preservatives": false}', '["https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400"]'),

      -- Fashion Hub Products
      (3, 3, 9, 'Men''s Cotton T-Shirt', 'Comfortable 100% cotton t-shirt', 19.99, 'TSHIRT-M-NAVY-L', '{"color": "Navy Blue", "size": "L", "material": "100% Cotton", "fit": "Regular"}', '["https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400"]'),
      (3, 3, 9, 'Men''s Jeans', 'Classic straight-fit denim jeans', 59.99, 'JEANS-M-BLUE-32', '{"color": "Dark Blue", "size": "32x32", "material": "98% Cotton, 2% Elastane", "fit": "Straight"}', '["https://images.unsplash.com/photo-1542272604-787c3835535d?w=400"]'),
      (3, 3, 10, 'Women''s Summer Dress', 'Elegant floral summer dress', 49.99, 'DRESS-W-FLORAL-M', '{"color": "Floral Print", "size": "M", "material": "Polyester blend", "occasion": "Casual", "sleeve": "Short"}', '["https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400"]'),
      (3, 3, 11, 'Running Shoes', 'Lightweight running shoes with cushioned sole', 89.99, 'SHOES-RUN-42', '{"color": "Black/White", "size": "42", "type": "Running", "brand": "SportMax", "gender": "Unisex"}', '["https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400"]'),

      -- Book Corner Products
      (4, 4, 13, 'The Great Gatsby', 'Classic American novel by F. Scott Fitzgerald', 12.99, 'BOOK-GATSBY-PB', '{"author": "F. Scott Fitzgerald", "pages": 180, "format": "Paperback", "language": "English", "isbn": "978-0-7432-7356-5"}', '["https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400"]'),
      (4, 4, 14, 'Learn Python Programming', 'Comprehensive guide to Python programming', 39.99, 'BOOK-PYTHON-HB', '{"author": "John Doe", "pages": 450, "format": "Hardcover", "level": "Beginner to Advanced", "edition": "3rd"}', '["https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=400"]'),
      (4, 4, 15, 'Children''s Picture Book', 'Colorful picture book for young readers', 8.99, 'BOOK-CHILD-PIC', '{"author": "Jane Smith", "pages": 32, "format": "Hardcover", "age_range": "3-7 years", "illustrated": true}', '["https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400"]')
      ON CONFLICT DO NOTHING
    `;

    // 7. Seed Product Inventory
    console.log("📊 Seeding product inventory...");
    await productsDB.exec`
      INSERT INTO product_inventory (product_id, quantity_available, low_stock_threshold) VALUES
      (1, 25, 5), (2, 30, 5), (3, 15, 3), (4, 20, 4), (5, 50, 10), (6, 35, 7),
      (7, 100, 20), (8, 75, 15), (9, 40, 8), (10, 60, 12), (11, 80, 16), (12, 90, 18),
      (13, 45, 9), (14, 35, 7), (15, 25, 5), (16, 40, 8), (17, 55, 11), (18, 30, 6), (19, 20, 4)
      ON CONFLICT DO NOTHING
    `;

    // 8. Seed Product Variants
    console.log("🎨 Seeding product variants...");
    await productsDB.exec`
      INSERT INTO product_variants (product_id, variant_name, variant_attributes, price_adjustment, sku) VALUES
      (1, '256GB Storage', '{"storage": "256GB"}', 100.00, 'IP15P-256'),
      (1, '512GB Storage', '{"storage": "512GB"}', 200.00, 'IP15P-512'),
      (2, '512GB Storage', '{"storage": "512GB"}', 100.00, 'SGS24-512'),
      (3, '512GB Storage', '{"storage": "512GB"}', 200.00, 'MBA-M3-512'),
      (13, 'Size S', '{"size": "S"}', 0.00, 'TSHIRT-M-NAVY-S'),
      (13, 'Size XL', '{"size": "XL"}', 2.00, 'TSHIRT-M-NAVY-XL'),
      (14, 'Size 30', '{"size": "30x32"}', 0.00, 'JEANS-M-BLUE-30'),
      (14, 'Size 34', '{"size": "34x32"}', 0.00, 'JEANS-M-BLUE-34'),
      (16, 'Size 40', '{"size": "40"}', 0.00, 'SHOES-RUN-40'),
      (16, 'Size 44', '{"size": "44"}', 0.00, 'SHOES-RUN-44')
      ON CONFLICT DO NOTHING
    `;

    console.log("✅ Database seeding completed successfully!");

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

export { seedData };

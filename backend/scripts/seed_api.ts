import { api } from "encore.dev/api";
import { seedData } from "./seed_data";

interface SeedResponse {
  success: boolean;
  message: string;
}

// API endpoint to seed the database with dummy data
export const seedDatabase = api<void, SeedResponse>(
  { expose: true, method: "POST", path: "/admin/seed-database" },
  async () => {
    try {
      await seedData();
      return {
        success: true,
        message: "Database seeded successfully with dummy data!"
      };
    } catch (error) {
      console.error("Failed to seed database:", error);
      return {
        success: false,
        message: `Failed to seed database: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
);

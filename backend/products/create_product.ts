import { api, APIError } from "encore.dev/api";
import { productsDB } from "./db";

interface CreateProductRequest {
  storeId: number;
  categoryId: number;
  subcategoryId?: number;
  name: string;
  description?: string;
  basePrice: number;
  sku?: string;
  attributes?: Record<string, any>;
  images?: string[];
  initialQuantity?: number;
}

interface CreateProductResponse {
  id: number;
  storeId: number;
  categoryId: number;
  subcategoryId: number | null;
  name: string;
  description: string | null;
  basePrice: number;
  sku: string | null;
  attributes: Record<string, any> | null;
  images: string[] | null;
  isActive: boolean;
}

// Creates a new product for a store.
export const createProduct = api<CreateProductRequest, CreateProductResponse>(
  { expose: true, method: "POST", path: "/products" },
  async (req) => {
    const { 
      storeId, 
      categoryId, 
      subcategoryId, 
      name, 
      description, 
      basePrice, 
      sku, 
      attributes, 
      images, 
      initialQuantity = 0 
    } = req;

    if (basePrice < 0) {
      throw APIError.invalidArgument("Base price cannot be negative");
    }

    const tx = await productsDB.begin();
    
    try {
      // Create product
      const productResult = await tx.queryRow<{ id: number }>`
        INSERT INTO products (store_id, category_id, subcategory_id, name, description, base_price, sku, attributes, images)
        VALUES (${storeId}, ${categoryId}, ${subcategoryId || null}, ${name}, ${description || null}, ${basePrice}, ${sku || null}, ${JSON.stringify(attributes || null)}, ${JSON.stringify(images || null)})
        RETURNING id
      `;

      if (!productResult) {
        throw new Error("Failed to create product");
      }

      // Create inventory record
      await tx.exec`
        INSERT INTO product_inventory (product_id, quantity_available)
        VALUES (${productResult.id}, ${initialQuantity})
      `;

      await tx.commit();

      return {
        id: productResult.id,
        storeId,
        categoryId,
        subcategoryId: subcategoryId || null,
        name,
        description: description || null,
        basePrice,
        sku: sku || null,
        attributes: attributes || null,
        images: images || null,
        isActive: true
      };
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }
);

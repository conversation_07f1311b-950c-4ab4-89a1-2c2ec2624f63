import { api, APIError } from "encore.dev/api";
import { productsDB } from "./db";

// Interfaces for variant management
interface CreateVariantRequest {
  productId: number;
  variantName: string;
  variantAttributes: Record<string, any>;
  priceAdjustment: number;
  sku?: string;
}

interface UpdateVariantRequest {
  productId: number;
  variantId: number;
  variantName?: string;
  variantAttributes?: Record<string, any>;
  priceAdjustment?: number;
  sku?: string;
  isActive?: boolean;
}

interface DeleteVariantRequest {
  productId: number;
  variantId: number;
}

interface ListVariantsRequest {
  productId: number;
}

interface ProductVariant {
  id: number;
  variantName: string;
  variantAttributes: Record<string, any>;
  priceAdjustment: number;
  sku: string | null;
  isActive: boolean;
  createdAt: string;
}

interface VariantResponse {
  variant: ProductVariant;
}

interface ListVariantsResponse {
  variants: ProductVariant[];
}

// Create a new product variant
export const createVariant = api<CreateVariantRequest, VariantResponse>(
  { expose: true, method: "POST", path: "/products/:productId/variants" },
  async (req) => {
    const { productId, variantName, variantAttributes, priceAdjustment, sku } = req;

    // Verify product exists
    const product = await productsDB.queryRow`
      SELECT id FROM products WHERE id = ${productId} AND is_active = true
    `;

    if (!product) {
      throw APIError.notFound("Product not found");
    }

    // Check if variant with same name already exists for this product
    const existingVariant = await productsDB.queryRow`
      SELECT id FROM product_variants
      WHERE product_id = ${productId} AND variant_name = ${variantName} AND is_active = true
    `;

    if (existingVariant) {
      throw APIError.invalidArgument("A variant with this name already exists for this product");
    }

    // Create the variant using template literal syntax like in the seeding script
    await productsDB.exec`
      INSERT INTO product_variants (product_id, variant_name, variant_attributes, price_adjustment, sku)
      VALUES (${productId}, ${variantName}, ${JSON.stringify(variantAttributes)}, ${priceAdjustment}, ${sku || null})
    `;

    const result = await productsDB.queryRow<{
      id: number;
      variant_name: string;
      variant_attributes: any;
      price_adjustment: string;
      sku: string | null;
      is_active: boolean;
      created_at: Date;
    }>`
      SELECT id, variant_name, variant_attributes, price_adjustment::text as price_adjustment, sku, is_active, created_at
      FROM product_variants
      WHERE product_id = ${productId} AND variant_name = ${variantName}
      ORDER BY created_at DESC
      LIMIT 1
    `;

    if (!result) {
      throw APIError.internal("Failed to create variant");
    }

    return {
      variant: {
        id: result.id,
        variantName: result.variant_name,
        variantAttributes: result.variant_attributes,
        priceAdjustment: parseFloat(result.price_adjustment),
        sku: result.sku,
        isActive: result.is_active,
        createdAt: result.created_at.toISOString()
      }
    };
  }
);

// Update an existing product variant
export const updateVariant = api<UpdateVariantRequest, VariantResponse>(
  { expose: true, method: "PUT", path: "/products/:productId/variants/:variantId" },
  async (req) => {
    const { productId, variantId, variantName, variantAttributes, priceAdjustment, sku, isActive } = req;

    // Verify product and variant exist
    const variant = await productsDB.queryRow`
      SELECT pv.id FROM product_variants pv
      JOIN products p ON pv.product_id = p.id
      WHERE pv.id = ${variantId} AND p.id = ${productId} AND p.is_active = true
    `;

    if (!variant) {
      throw APIError.notFound("Product variant not found");
    }

    // Build update query dynamically
    const updates: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (variantName !== undefined) {
      updates.push(`variant_name = $${paramIndex}`);
      values.push(variantName);
      paramIndex++;
    }

    if (variantAttributes !== undefined) {
      updates.push(`variant_attributes = $${paramIndex}`);
      values.push(JSON.stringify(variantAttributes));
      paramIndex++;
    }

    if (priceAdjustment !== undefined) {
      updates.push(`price_adjustment = $${paramIndex}`);
      values.push(priceAdjustment);
      paramIndex++;
    }

    if (sku !== undefined) {
      updates.push(`sku = $${paramIndex}`);
      values.push(sku);
      paramIndex++;
    }

    if (isActive !== undefined) {
      updates.push(`is_active = $${paramIndex}`);
      values.push(isActive);
      paramIndex++;
    }

    if (updates.length === 0) {
      throw APIError.invalidArgument("No fields to update");
    }

    const updateQuery = `
      UPDATE product_variants
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, variant_name, variant_attributes, price_adjustment::text as price_adjustment, sku, is_active, created_at
    `;
    values.push(variantId);

    const result = await productsDB.rawQueryRow<{
      id: number;
      variant_name: string;
      variant_attributes: any;
      price_adjustment: string;
      sku: string | null;
      is_active: boolean;
      created_at: Date;
    }>(updateQuery, ...values);

    if (!result) {
      throw APIError.internal("Failed to update variant");
    }

    return {
      variant: {
        id: result.id,
        variantName: result.variant_name,
        variantAttributes: result.variant_attributes,
        priceAdjustment: parseFloat(result.price_adjustment),
        sku: result.sku,
        isActive: result.is_active,
        createdAt: result.created_at.toISOString()
      }
    };
  }
);

// Delete a product variant (soft delete)
export const deleteVariant = api<DeleteVariantRequest, { success: boolean }>(
  { expose: true, method: "DELETE", path: "/products/:productId/variants/:variantId" },
  async (req) => {
    const { productId, variantId } = req;

    // Verify product and variant exist
    const variant = await productsDB.queryRow`
      SELECT pv.id FROM product_variants pv
      JOIN products p ON pv.product_id = p.id
      WHERE pv.id = ${variantId} AND p.id = ${productId} AND p.is_active = true
    `;

    if (!variant) {
      throw APIError.notFound("Product variant not found");
    }

    // Soft delete the variant
    await productsDB.exec`
      UPDATE product_variants
      SET is_active = false
      WHERE id = ${variantId}
    `;

    return { success: true };
  }
);

// List all variants for a product
export const listVariants = api<ListVariantsRequest, ListVariantsResponse>(
  { expose: true, method: "GET", path: "/products/:productId/variants" },
  async (req) => {
    const { productId } = req;

    // Verify product exists
    const product = await productsDB.queryRow`
      SELECT id FROM products WHERE id = ${productId} AND is_active = true
    `;

    if (!product) {
      throw APIError.notFound("Product not found");
    }

    // Get all variants for the product
    const variants = await productsDB.queryAll<{
      id: number;
      variant_name: string;
      variant_attributes: any;
      price_adjustment: string;
      sku: string | null;
      is_active: boolean;
      created_at: Date;
    }>`
      SELECT id, variant_name, variant_attributes, price_adjustment::text as price_adjustment, sku, is_active, created_at
      FROM product_variants
      WHERE product_id = ${productId}
      ORDER BY created_at DESC
    `;

    return {
      variants: variants.map(v => ({
        id: v.id,
        variantName: v.variant_name,
        variantAttributes: v.variant_attributes,
        priceAdjustment: parseFloat(v.price_adjustment),
        sku: v.sku,
        isActive: v.is_active,
        createdAt: v.created_at.toISOString()
      }))
    };
  }
);

import { api, APIError } from "encore.dev/api";
import { prisma, type Product, type ProductVariant } from "../lib/prisma";

interface GetProductWithPrismaRequest {
  id: number;
}

interface ProductWithVariantsResponse {
  id: number;
  name: string;
  description: string | null;
  basePrice: number;
  sku: string | null;
  attributes: any;
  images: any;
  isActive: boolean;
  store: {
    id: number;
    name: string;
    vendor: {
      businessName: string;
    };
  };
  category: {
    id: number;
    name: string;
  };
  subcategory: {
    id: number;
    name: string;
  } | null;
  inventory: {
    quantityAvailable: number;
    reservedQuantity: number;
    lowStockThreshold: number;
  } | null;
  variants: Array<{
    id: number;
    variantName: string;
    variantAttributes: any;
    priceAdjustment: number;
    sku: string | null;
    isActive: boolean;
  }>;
  createdAt: string;
}

interface ListProductsWithPrismaRequest {
  categoryId?: number;
  storeId?: number;
  limit?: number;
  offset?: number;
}

interface ListProductsWithPrismaResponse {
  products: ProductWithVariantsResponse[];
  total: number;
}

// Get a single product with all related data using Prisma
export const getProductWithPrisma = api<GetProductWithPrismaRequest, ProductWithVariantsResponse>(
  { expose: true, method: "GET", path: "/products/prisma/:id" },
  async (req) => {
    const { id } = req;

    const product = await prisma.product.findFirst({
      where: {
        id: BigInt(id),
        isActive: true,
      },
      include: {
        store: {
          include: {
            vendor: {
              select: {
                businessName: true,
              },
            },
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        subcategory: {
          select: {
            id: true,
            name: true,
          },
        },
        inventory: {
          select: {
            quantityAvailable: true,
            reservedQuantity: true,
            lowStockThreshold: true,
          },
        },
        variants: {
          where: {
            isActive: true,
          },
          select: {
            id: true,
            variantName: true,
            variantAttributes: true,
            priceAdjustment: true,
            sku: true,
            isActive: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    if (!product) {
      throw APIError.notFound("Product not found");
    }

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      basePrice: Number(product.basePrice),
      sku: product.sku,
      attributes: product.attributes,
      images: product.images,
      isActive: product.isActive,
      store: {
        id: Number(product.store.id),
        name: product.store.name,
        vendor: {
          businessName: product.store.vendor.businessName,
        },
      },
      category: {
        id: Number(product.category.id),
        name: product.category.name,
      },
      subcategory: product.subcategory ? {
        id: Number(product.subcategory.id),
        name: product.subcategory.name,
      } : null,
      inventory: product.inventory ? {
        quantityAvailable: product.inventory.quantityAvailable,
        reservedQuantity: product.inventory.reservedQuantity,
        lowStockThreshold: product.inventory.lowStockThreshold,
      } : null,
      variants: product.variants.map(variant => ({
        id: Number(variant.id),
        variantName: variant.variantName,
        variantAttributes: variant.variantAttributes,
        priceAdjustment: Number(variant.priceAdjustment),
        sku: variant.sku,
        isActive: variant.isActive,
      })),
      createdAt: product.createdAt.toISOString(),
    };
  }
);

// List products with filtering using Prisma
export const listProductsWithPrisma = api<ListProductsWithPrismaRequest, ListProductsWithPrismaResponse>(
  { expose: true, method: "GET", path: "/products/prisma" },
  async (req) => {
    const { categoryId, storeId, limit = 20, offset = 0 } = req;

    const where: any = {
      isActive: true,
    };

    if (categoryId) {
      where.categoryId = BigInt(categoryId);
    }

    if (storeId) {
      where.storeId = BigInt(storeId);
    }

    // Get total count
    const total = await prisma.product.count({ where });

    // Get products with relations
    const products = await prisma.product.findMany({
      where,
      include: {
        store: {
          include: {
            vendor: {
              select: {
                businessName: true,
              },
            },
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        subcategory: {
          select: {
            id: true,
            name: true,
          },
        },
        inventory: {
          select: {
            quantityAvailable: true,
            reservedQuantity: true,
            lowStockThreshold: true,
          },
        },
        variants: {
          where: {
            isActive: true,
          },
          select: {
            id: true,
            variantName: true,
            variantAttributes: true,
            priceAdjustment: true,
            sku: true,
            isActive: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      skip: offset,
    });

    return {
      products: products.map(product => ({
        id: Number(product.id),
        name: product.name,
        description: product.description,
        basePrice: Number(product.basePrice),
        sku: product.sku,
        attributes: product.attributes,
        images: product.images,
        isActive: product.isActive,
        store: {
          id: Number(product.store.id),
          name: product.store.name,
          vendor: {
            businessName: product.store.vendor.businessName,
          },
        },
        category: {
          id: Number(product.category.id),
          name: product.category.name,
        },
        subcategory: product.subcategory ? {
          id: Number(product.subcategory.id),
          name: product.subcategory.name,
        } : null,
        inventory: product.inventory ? {
          quantityAvailable: product.inventory.quantityAvailable,
          reservedQuantity: product.inventory.reservedQuantity,
          lowStockThreshold: product.inventory.lowStockThreshold,
        } : null,
        variants: product.variants.map(variant => ({
          id: Number(variant.id),
          variantName: variant.variantName,
          variantAttributes: variant.variantAttributes,
          priceAdjustment: Number(variant.priceAdjustment),
          sku: variant.sku,
          isActive: variant.isActive,
        })),
        createdAt: product.createdAt.toISOString(),
      })),
      total,
    };
  }
);

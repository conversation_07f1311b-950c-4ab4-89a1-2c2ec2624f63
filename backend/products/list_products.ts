import { api } from "encore.dev/api";
import { Query } from "encore.dev/api";
import { productsDB } from "./db";

interface ListProductsRequest {
  categoryId?: Query<number>;
  subcategoryId?: Query<number>;
  storeId?: Query<number>;
  search?: Query<string>;
  limit?: Query<number>;
  offset?: Query<number>;
}

interface Product {
  id: number;
  storeId: number;
  storeName: string;
  categoryId: number;
  categoryName: string;
  subcategoryId: number | null;
  subcategoryName: string | null;
  name: string;
  description: string | null;
  basePrice: number;
  sku: string | null;
  attributes: Record<string, any> | null;
  images: string[] | null;
  quantityAvailable: number;
  isActive: boolean;
}

interface ListProductsResponse {
  products: Product[];
  total: number;
}

// Retrieves products with optional filtering and search.
export const listProducts = api<ListProductsRequest, ListProductsResponse>(
  { expose: true, method: "GET", path: "/products" },
  async (req) => {
    const { categoryId, subcategoryId, storeId, search, limit = 20, offset = 0 } = req;

    let whereConditions = ["p.is_active = true"];
    let params: any[] = [];
    let paramIndex = 1;

    if (categoryId) {
      whereConditions.push(`p.category_id = $${paramIndex}`);
      params.push(categoryId);
      paramIndex++;
    }

    if (subcategoryId) {
      whereConditions.push(`p.subcategory_id = $${paramIndex}`);
      params.push(subcategoryId);
      paramIndex++;
    }

    if (storeId) {
      whereConditions.push(`p.store_id = $${paramIndex}`);
      params.push(storeId);
      paramIndex++;
    }

    if (search) {
      whereConditions.push(`(to_tsvector('english', p.name) @@ plainto_tsquery('english', $${paramIndex}) OR to_tsvector('english', COALESCE(p.description, '')) @@ plainto_tsquery('english', $${paramIndex}))`);
      params.push(search);
      paramIndex++;
    }

    const whereClause = whereConditions.join(" AND ");

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE ${whereClause}
    `;
    
    const countResult = await productsDB.rawQueryRow<{ total: number }>(countQuery, ...params);
    const total = countResult?.total || 0;

    // Get products with proper LEFT JOINs and null handling
    const productsQuery = `
      SELECT 
        p.id,
        p.store_id,
        COALESCE(s.name, 'Unknown Store') as store_name,
        p.category_id,
        COALESCE(c.name, 'Unknown Category') as category_name,
        p.subcategory_id,
        sc.name as subcategory_name,
        p.name,
        p.description,
        p.base_price,
        p.sku,
        p.attributes,
        p.images,
        COALESCE(pi.quantity_available, 0) as quantity_available,
        p.is_active
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN subcategories sc ON p.subcategory_id = sc.id
      LEFT JOIN product_inventory pi ON p.id = pi.product_id
      WHERE ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(limit, offset);

    const products = await productsDB.rawQueryAll<{
      id: number;
      store_id: number;
      store_name: string;
      category_id: number;
      category_name: string;
      subcategory_id: number | null;
      subcategory_name: string | null;
      name: string;
      description: string | null;
      base_price: number;
      sku: string | null;
      attributes: any;
      images: any;
      quantity_available: number;
      is_active: boolean;
    }>(productsQuery, ...params);

    return {
      products: products.map(p => ({
        id: p.id,
        storeId: p.store_id,
        storeName: p.store_name,
        categoryId: p.category_id,
        categoryName: p.category_name,
        subcategoryId: p.subcategory_id,
        subcategoryName: p.subcategory_name,
        name: p.name,
        description: p.description,
        basePrice: p.base_price,
        sku: p.sku,
        attributes: p.attributes,
        images: p.images,
        quantityAvailable: p.quantity_available,
        isActive: p.is_active
      })),
      total
    };
  }
);

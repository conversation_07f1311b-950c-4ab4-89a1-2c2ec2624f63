import { api } from "encore.dev/api";
import { Query } from "encore.dev/api";
import { productsDB } from "./db";

interface ListProductsRequest {
  categoryId?: Query<number>;
  subcategoryId?: Query<number>;
  storeId?: Query<number>;
  search?: Query<string>;
  limit?: Query<number>;
  offset?: Query<number>;
}

interface Product {
  id: number;
  storeId: number;
  storeName: string;
  categoryId: number;
  categoryName: string;
  subcategoryId: number | null;
  subcategoryName: string | null;
  name: string;
  description: string | null;
  basePrice: number;
  sku: string | null;
  attributes: Record<string, any> | null;
  images: string[] | null;
  quantityAvailable: number;
  isActive: boolean;
}

interface ListProductsResponse {
  products: Product[];
  total: number;
}

// Retrieves products with optional filtering and search.
export const listProducts = api<ListProductsRequest, ListProductsResponse>(
  { expose: true, method: "GET", path: "/products" },
  async (req) => {
    const { categoryId, subcategoryId, storeId, search, limit = 20, offset = 0 } = req;

    // Get products with proper decimal handling
    const products = await productsDB.queryAll<{
      id: number;
      store_id: number;
      category_id: number;
      subcategory_id: number | null;
      name: string;
      description: string | null;
      base_price: string;
      sku: string | null;
      attributes: any;
      images: any;
      is_active: boolean;
    }>`
      SELECT
        p.id,
        p.store_id,
        p.category_id,
        p.subcategory_id,
        p.name,
        p.description,
        p.base_price::text as base_price,
        p.sku,
        p.attributes,
        p.images,
        p.is_active
      FROM products p
      WHERE p.is_active = true
      ORDER BY p.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    // Get inventory separately
    const inventory = await productsDB.queryAll<{
      product_id: number;
      quantity_available: number;
    }>`
      SELECT product_id, quantity_available
      FROM product_inventory
    `;

    const inventoryMap = new Map(inventory.map(i => [i.product_id, i.quantity_available]));

    const total = products.length;

    return {
      products: products.map(p => ({
        id: p.id,
        storeId: p.store_id,
        storeName: `Store ${p.store_id}`,
        categoryId: p.category_id,
        categoryName: `Category ${p.category_id}`,
        subcategoryId: p.subcategory_id,
        subcategoryName: p.subcategory_id ? `Subcategory ${p.subcategory_id}` : null,
        name: p.name,
        description: p.description,
        basePrice: parseFloat(p.base_price),
        sku: p.sku,
        attributes: p.attributes,
        images: p.images,
        quantityAvailable: inventoryMap.get(p.id) || 0,
        isActive: p.is_active
      })),
      total
    };
  }
);

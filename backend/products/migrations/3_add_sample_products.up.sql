-- Insert sample products
INSERT INTO products (store_id, category_id, subcategory_id, name, description, base_price, sku, attributes, images) VALUES
(1, 1, 1, 'iPhone 15 Pro', 'Latest Apple smartphone with advanced features', 999.99, 'IP15P-128', '{"color": "Space Black", "storage": "128GB", "warranty": "1 year"}', '["https://example.com/iphone15pro.jpg"]'),
(1, 1, 2, 'MacBook Air M2', 'Lightweight laptop with M2 chip', 1199.99, 'MBA-M2-256', '{"color": "Silver", "storage": "256GB SSD", "ram": "8GB", "warranty": "1 year"}', '["https://example.com/macbookair.jpg"]'),
(1, 1, 3, 'AirPods Pro 2', 'Wireless earbuds with noise cancellation', 249.99, 'APP2-WHITE', '{"color": "White", "battery_life": "6 hours", "noise_cancellation": true}', '["https://example.com/airpodspro.jpg"]'),
(2, 2, 4, 'Organic Bananas', 'Fresh organic bananas', 2.99, 'ORG-BAN-1LB', '{"organic": true, "weight": "1 lb", "origin": "Ecuador"}', '["https://example.com/bananas.jpg"]'),
(2, 2, 5, 'Whole Milk', 'Fresh whole milk', 3.49, 'MILK-WHOLE-1GAL', '{"fat_content": "3.25%", "volume": "1 gallon", "expiry_days": 7}', '["https://example.com/milk.jpg"]'),
(2, 2, 6, 'Potato Chips', 'Crispy potato chips', 1.99, 'CHIPS-ORIG-150G', '{"flavor": "Original", "weight": "150g", "gluten_free": false}', '["https://example.com/chips.jpg"]'),
(3, 3, 7, 'Men''s T-Shirt', 'Comfortable cotton t-shirt', 19.99, 'TSHIRT-M-BLUE-L', '{"color": "Blue", "size": "L", "material": "100% Cotton"}', '["https://example.com/tshirt.jpg"]'),
(3, 3, 8, 'Women''s Dress', 'Elegant summer dress', 49.99, 'DRESS-W-RED-M', '{"color": "Red", "size": "M", "material": "Polyester blend", "occasion": "Casual"}', '["https://example.com/dress.jpg"]'),
(3, 3, 9, 'Running Shoes', 'Comfortable running shoes', 79.99, 'SHOES-RUN-42', '{"color": "Black/White", "size": "42", "type": "Running", "brand": "SportMax"}', '["https://example.com/shoes.jpg"]'),
(4, 4, 10, 'The Great Gatsby', 'Classic American novel', 12.99, 'BOOK-GATSBY-PB', '{"author": "F. Scott Fitzgerald", "pages": 180, "format": "Paperback", "language": "English"}', '["https://example.com/gatsby.jpg"]'),
(4, 4, 11, 'Learn Python Programming', 'Comprehensive Python guide', 39.99, 'BOOK-PYTHON-HB', '{"author": "John Doe", "pages": 450, "format": "Hardcover", "level": "Beginner to Advanced"}', '["https://example.com/python.jpg"]')
ON CONFLICT DO NOTHING;

-- Insert inventory for products
INSERT INTO product_inventory (product_id, quantity_available, low_stock_threshold) VALUES
(1, 25, 5),
(2, 15, 3),
(3, 50, 10),
(4, 100, 20),
(5, 30, 5),
(6, 75, 15),
(7, 40, 8),
(8, 20, 4),
(9, 35, 7),
(10, 60, 12),
(11, 25, 5)
ON CONFLICT DO NOTHING;

-- Insert some product variants
INSERT INTO product_variants (product_id, variant_name, variant_attributes, price_adjustment, sku) VALUES
(1, '256GB Storage', '{"storage": "256GB"}', 100.00, 'IP15P-256'),
(1, '512GB Storage', '{"storage": "512GB"}', 200.00, 'IP15P-512'),
(7, 'Size S', '{"size": "S"}', 0.00, 'TSHIRT-M-BLUE-S'),
(7, 'Size XL', '{"size": "XL"}', 2.00, 'TSHIRT-M-BLUE-XL'),
(9, 'Size 40', '{"size": "40"}', 0.00, 'SHOES-RUN-40'),
(9, 'Size 44', '{"size": "44"}', 0.00, 'SHOES-RUN-44')
ON CONFLICT DO NOTHING;

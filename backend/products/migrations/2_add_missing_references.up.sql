-- Add missing tables that products service references
-- Note: In a production system, these would be managed by their respective services
-- For this MVP, we'll create them in the products database to avoid cross-service dependencies

-- Categories table (referenced by products)
CREATE TABLE IF NOT EXISTS categories (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  icon VARCHAR(100),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Subcategories table (referenced by products)
CREATE TABLE IF NOT EXISTS subcategories (
  id BIGSERIAL PRIMARY KEY,
  category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Stores table (referenced by products)
CREATE TABLE IF NOT EXISTS stores (
  id BIGSERIAL PRIMARY KEY,
  vendor_id BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  address TEXT NOT NULL,
  phone_number VARCHAR(20),
  operating_hours JSONB,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_subcategories_category_id ON subcategories(category_id);
CREATE INDEX IF NOT EXISTS idx_stores_vendor_id ON stores(vendor_id);

-- Insert sample data for development
INSERT INTO categories (name, description, icon) VALUES
('Electronics', 'Electronic devices and gadgets', '📱'),
('Groceries', 'Food and daily essentials', '🛒'),
('Clothing', 'Apparel and fashion items', '👕'),
('Books', 'Books and educational materials', '📚'),
('Home & Garden', 'Home improvement and gardening supplies', '🏠'),
('Health & Beauty', 'Health and beauty products', '💄')
ON CONFLICT DO NOTHING;

INSERT INTO subcategories (category_id, name, description) VALUES
(1, 'Smartphones', 'Mobile phones and accessories'),
(1, 'Laptops', 'Portable computers'),
(1, 'Headphones', 'Audio devices'),
(2, 'Fruits & Vegetables', 'Fresh produce'),
(2, 'Dairy Products', 'Milk, cheese, yogurt'),
(2, 'Snacks', 'Packaged snacks and treats'),
(3, 'Men''s Clothing', 'Clothing for men'),
(3, 'Women''s Clothing', 'Clothing for women'),
(3, 'Shoes', 'Footwear for all'),
(4, 'Fiction', 'Novels and stories'),
(4, 'Non-Fiction', 'Educational and reference books'),
(5, 'Furniture', 'Home furniture'),
(5, 'Garden Tools', 'Gardening equipment'),
(6, 'Skincare', 'Skin care products'),
(6, 'Makeup', 'Cosmetics and beauty products')
ON CONFLICT DO NOTHING;

INSERT INTO stores (vendor_id, name, description, address, phone_number) VALUES
(1, 'Tech World Main Store', 'Main electronics store', '123 Main St, Tech City', '+**********'),
(2, 'Fresh Market Downtown', 'Downtown grocery store', '456 Market Ave, Green Valley', '+1234567891'),
(3, 'Fashion Hub Outlet', 'Fashion outlet store', '789 Style Blvd, Fashion District', '+1234567892'),
(4, 'Book Corner Library', 'Main bookstore', '321 Library St, Knowledge Town', '+1234567893')
ON CONFLICT DO NOTHING;

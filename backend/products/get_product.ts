import { api, APIError } from "encore.dev/api";
import { productsDB } from "./db";

interface GetProductRequest {
  id: number;
}

interface ProductVariant {
  id: number;
  variantName: string;
  variantAttributes: Record<string, any>;
  priceAdjustment: number;
  sku: string | null;
  isActive: boolean;
}

interface GetProductResponse {
  id: number;
  storeId: number;
  storeName: string;
  categoryId: number;
  categoryName: string;
  subcategoryId: number | null;
  subcategoryName: string | null;
  name: string;
  description: string | null;
  basePrice: number;
  sku: string | null;
  attributes: Record<string, any> | null;
  images: string[] | null;
  quantityAvailable: number;
  reservedQuantity: number;
  lowStockThreshold: number;
  variants: ProductVariant[];
  isActive: boolean;
}

// Retrieves detailed information about a specific product.
export const getProduct = api<GetProductRequest, GetProductResponse>(
  { expose: true, method: "GET", path: "/products/:id" },
  async (req) => {
    const { id } = req;

    const product = await productsDB.queryRow<{
      id: number;
      store_id: number;
      store_name: string;
      category_id: number;
      category_name: string;
      subcategory_id: number | null;
      subcategory_name: string | null;
      name: string;
      description: string | null;
      base_price: number;
      sku: string | null;
      attributes: any;
      images: any;
      quantity_available: number;
      reserved_quantity: number;
      low_stock_threshold: number;
      is_active: boolean;
    }>`
      SELECT 
        p.id,
        p.store_id,
        COALESCE(s.name, 'Unknown Store') as store_name,
        p.category_id,
        COALESCE(c.name, 'Unknown Category') as category_name,
        p.subcategory_id,
        sc.name as subcategory_name,
        p.name,
        p.description,
        p.base_price,
        p.sku,
        p.attributes,
        p.images,
        COALESCE(pi.quantity_available, 0) as quantity_available,
        COALESCE(pi.reserved_quantity, 0) as reserved_quantity,
        COALESCE(pi.low_stock_threshold, 5) as low_stock_threshold,
        p.is_active
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN subcategories sc ON p.subcategory_id = sc.id
      LEFT JOIN product_inventory pi ON p.id = pi.product_id
      WHERE p.id = ${id} AND p.is_active = true
    `;

    if (!product) {
      throw APIError.notFound("Product not found");
    }

    // Get product variants
    const variants = await productsDB.queryAll<{
      id: number;
      variant_name: string;
      variant_attributes: any;
      price_adjustment: number;
      sku: string | null;
      is_active: boolean;
    }>`
      SELECT id, variant_name, variant_attributes, price_adjustment, sku, is_active
      FROM product_variants
      WHERE product_id = ${id} AND is_active = true
      ORDER BY variant_name
    `;

    return {
      id: product.id,
      storeId: product.store_id,
      storeName: product.store_name,
      categoryId: product.category_id,
      categoryName: product.category_name,
      subcategoryId: product.subcategory_id,
      subcategoryName: product.subcategory_name,
      name: product.name,
      description: product.description,
      basePrice: product.base_price,
      sku: product.sku,
      attributes: product.attributes,
      images: product.images,
      quantityAvailable: product.quantity_available,
      reservedQuantity: product.reserved_quantity,
      lowStockThreshold: product.low_stock_threshold,
      variants: variants.map(v => ({
        id: v.id,
        variantName: v.variant_name,
        variantAttributes: v.variant_attributes,
        priceAdjustment: v.price_adjustment,
        sku: v.sku,
        isActive: v.is_active
      })),
      isActive: product.is_active
    };
  }
);

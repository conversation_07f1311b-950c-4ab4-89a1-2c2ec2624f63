import { api } from "encore.dev/api";
import { vendorsDB } from "./db";

interface Vendor {
  id: number;
  businessName: string;
  ownerName: string;
  phoneNumber: string;
  email: string | null;
  address: string;
  description: string | null;
  isActive: boolean;
  isVerified: boolean;
  storeCount: number;
}

interface ListVendorsResponse {
  vendors: Vendor[];
}

// Retrieves all active vendors with their store counts.
export const listVendors = api<void, ListVendorsResponse>(
  { expose: true, method: "GET", path: "/vendors" },
  async () => {
    const vendors = await vendorsDB.queryAll<{
      id: number;
      business_name: string;
      owner_name: string;
      phone_number: string;
      email: string | null;
      address: string;
      description: string | null;
      is_active: boolean;
      is_verified: boolean;
      store_count: number;
    }>`
      SELECT 
        v.id,
        v.business_name,
        v.owner_name,
        v.phone_number,
        v.email,
        v.address,
        v.description,
        v.is_active,
        v.is_verified,
        COALESCE(s.store_count, 0) as store_count
      FROM vendors v
      LEFT JOIN (
        SELECT vendor_id, COUNT(*) as store_count
        FROM stores
        WHERE is_active = true
        GROUP BY vendor_id
      ) s ON v.id = s.vendor_id
      WHERE v.is_active = true
      ORDER BY v.business_name
    `;

    return {
      vendors: vendors.map(v => ({
        id: v.id,
        businessName: v.business_name,
        ownerName: v.owner_name,
        phoneNumber: v.phone_number,
        email: v.email,
        address: v.address,
        description: v.description,
        isActive: v.is_active,
        isVerified: v.is_verified,
        storeCount: v.store_count
      }))
    };
  }
);

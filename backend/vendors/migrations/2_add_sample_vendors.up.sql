-- Insert sample vendors
INSERT INTO vendors (user_id, business_name, owner_name, phone_number, email, address, description, is_verified) VALUES
(1, 'Tech World', '<PERSON>', '+1234567890', '<EMAIL>', '123 Main St, Tech City', 'Your one-stop shop for all electronic needs', true),
(2, 'Fresh Market', '<PERSON> Johnson', '+1234567891', '<EMAIL>', '456 Market Ave, Green Valley', 'Fresh groceries and organic produce', true),
(3, 'Fashion Hub', '<PERSON>', '+1234567892', '<EMAIL>', '789 Style Blvd, Fashion District', 'Trendy clothing for all ages', false),
(4, 'Book Corner', '<PERSON> Davis', '+1234567893', '<EMAIL>', '321 Library St, Knowledge Town', 'Wide selection of books and educational materials', true)
ON CONFLICT DO NOTHING;

-- Insert sample stores
INSERT INTO stores (vendor_id, name, description, address, phone_number) VALUES
(1, 'Tech World Main Store', 'Main electronics store', '123 Main St, Tech City', '+1234567890'),
(2, 'Fresh Market Downtown', 'Downtown grocery store', '456 Market Ave, Green Valley', '+1234567891'),
(3, 'Fashion Hub Outlet', 'Fashion outlet store', '789 Style Blvd, Fashion District', '+1234567892'),
(4, 'Book Corner Library', 'Main bookstore', '321 Library St, Knowledge Town', '+1234567893')
ON CONFLICT DO NOTHING;

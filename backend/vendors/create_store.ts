import { api, APIError } from "encore.dev/api";
import { vendorsDB } from "./db";

interface CreateStoreRequest {
  vendorId: number;
  name: string;
  description?: string;
  address: string;
  phoneNumber?: string;
  operatingHours?: Record<string, any>;
}

interface CreateStoreResponse {
  id: number;
  vendorId: number;
  name: string;
  description: string | null;
  address: string;
  phoneNumber: string | null;
  operatingHours: Record<string, any> | null;
  isActive: boolean;
}

// Creates a new store for a vendor.
export const createStore = api<CreateStoreRequest, CreateStoreResponse>(
  { expose: true, method: "POST", path: "/vendors/stores" },
  async (req) => {
    const { vendorId, name, description, address, phoneNumber, operatingHours } = req;

    // Verify vendor exists
    const vendor = await vendorsDB.queryRow`
      SELECT id FROM vendors WHERE id = ${vendorId} AND is_active = true
    `;

    if (!vendor) {
      throw APIError.notFound("Vendor not found");
    }

    const result = await vendorsDB.queryRow<{ id: number }>`
      INSERT INTO stores (vendor_id, name, description, address, phone_number, operating_hours)
      VALUES (${vendorId}, ${name}, ${description || null}, ${address}, ${phoneNumber || null}, ${JSON.stringify(operatingHours || null)})
      RETURNING id
    `;

    if (!result) {
      throw new Error("Failed to create store");
    }

    return {
      id: result.id,
      vendorId,
      name,
      description: description || null,
      address,
      phoneNumber: phoneNumber || null,
      operatingHours: operatingHours || null,
      isActive: true
    };
  }
);

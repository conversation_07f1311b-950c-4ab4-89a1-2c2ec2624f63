import { api } from "encore.dev/api";
import { vendorsDB } from "./db";

interface RegisterVendorRequest {
  userId: number;
  businessName: string;
  ownerName: string;
  phoneNumber: string;
  email?: string;
  address: string;
  description?: string;
}

interface RegisterVendorResponse {
  id: number;
  businessName: string;
  ownerName: string;
  phoneNumber: string;
  email: string | null;
  address: string;
  description: string | null;
  isActive: boolean;
  isVerified: boolean;
}

// Registers a new vendor in the platform.
export const registerVendor = api<RegisterVendorRequest, RegisterVendorResponse>(
  { expose: true, method: "POST", path: "/vendors/register" },
  async (req) => {
    const { userId, businessName, ownerName, phoneNumber, email, address, description } = req;

    const result = await vendorsDB.queryRow<{ id: number }>`
      INSERT INTO vendors (user_id, business_name, owner_name, phone_number, email, address, description)
      VALUES (${userId}, ${businessName}, ${ownerName}, ${phoneNumber}, ${email || null}, ${address}, ${description || null})
      RETURNING id
    `;

    if (!result) {
      throw new Error("Failed to register vendor");
    }

    return {
      id: result.id,
      businessName,
      ownerName,
      phoneNumber,
      email: email || null,
      address,
      description: description || null,
      isActive: true,
      isVerified: false
    };
  }
);

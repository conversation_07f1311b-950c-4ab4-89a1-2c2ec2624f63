generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  phone     String   @unique
  name      String?
  isVendor  <PERSON>an  @default(false)
  address   String?
  orders    Order[]
  store     Store?
  createdAt DateTime @default(now())
}

model Store {
  id          String   @id @default(cuid())
  name        String
  owner       User     @relation(fields: [ownerId], references: [id])
  ownerId     String   @unique
  products    Product[]
  createdAt   DateTime @default(now())
  location    String?
}

model Category {
  id          String     @id @default(cuid())
  name        String
  slug        String     @unique
  parent      Category?  @relation("SubCategory", fields: [parentId], references: [id])
  parentId    String?
  children    Category[] @relation("SubCategory")
  products    Product[]
}

model Product {
  id          String     @id @default(cuid())
  name        String
  description String?
  price       Float
  image       String?
  store       Store      @relation(fields: [storeId], references: [id])
  storeId     String
  category    Category   @relation(fields: [categoryId], references: [id])
  categoryId  String
  attributes  Json?
  stock       Int        @default(0)
  createdAt   DateTime   @default(now())
  orderItems  OrderItem[]
}

model Order {
  id          String      @id @default(cuid())
  user        User        @relation(fields: [userId], references: [id])
  userId      String
  total       Float
  status      String      @default("pending") // pending, shipped, delivered
  items       OrderItem[]
  createdAt   DateTime    @default(now())
  address     String?
  paymentMode String      @default("COD")
}

model OrderItem {
  id         String   @id @default(cuid())
  order      Order    @relation(fields: [orderId], references: [id])
  orderId    String
  product    Product  @relation(fields: [productId], references: [id])
  productId  String
  quantity   Int
  price      Float
}

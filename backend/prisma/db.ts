import { SQLDatabase } from "encore.dev/storage/sqldb";
import { PrismaClient } from "@prisma/client";

// Define a database named 'hyperlocal_commerce', using the database migrations
// in the "./migrations" folder. Set `source` to `prisma` to let Encore know 
// that the migrations are generated by Prisma.
const DB = new SQLDatabase('hyperlocal_commerce', {
  migrations: {
    path: './migrations',
    source: 'prisma',
  },
});

// Setup prisma client with connection string from Encore
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: DB.connectionString,
    },
  },
});

export { prisma, DB };

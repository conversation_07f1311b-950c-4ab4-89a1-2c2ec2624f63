// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
// Auth models
model User {
  id          BigInt    @id @default(autoincrement())
  phoneNumber String    @unique @map("phone_number") @db.VarChar(20)
  name        String?   @db.VarChar(255)
  email       String?   @db.VarChar(255)
  role        String    @default("customer") @db.VarChar(50)
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  vendors     Vendor[]
  orders      Order[]

  @@map("users")
}

model OtpCode {
  id          BigInt    @id @default(autoincrement())
  phoneNumber String    @map("phone_number") @db.VarChar(20)
  code        String    @db.VarChar(6)
  expiresAt   DateTime  @map("expires_at") @db.Timestamptz(6)
  isUsed      Boolean   @default(false) @map("is_used")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  @@index([phoneNumber], map: "idx_otp_codes_phone_number")
  @@index([expiresAt], map: "idx_otp_codes_expires_at")
  @@map("otp_codes")
}

// Vendor models
model Vendor {
  id           BigInt    @id @default(autoincrement())
  userId       BigInt    @map("user_id")
  businessName String    @map("business_name") @db.VarChar(255)
  ownerName    String    @map("owner_name") @db.VarChar(255)
  phoneNumber  String    @map("phone_number") @db.VarChar(20)
  email        String?   @db.VarChar(255)
  address      String    @db.Text
  description  String?   @db.Text
  isActive     Boolean   @default(true) @map("is_active")
  isVerified   Boolean   @default(false) @map("is_verified")
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  user         User      @relation(fields: [userId], references: [id])
  stores       Store[]

  @@index([userId], map: "idx_vendors_user_id")
  @@map("vendors")
}

model Store {
  id             BigInt    @id @default(autoincrement())
  vendorId       BigInt    @map("vendor_id")
  name           String    @db.VarChar(255)
  description    String?   @db.Text
  address        String    @db.Text
  phoneNumber    String?   @map("phone_number") @db.VarChar(20)
  operatingHours Json?     @map("operating_hours")
  isActive       Boolean   @default(true) @map("is_active")
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  vendor         Vendor    @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  products       Product[]

  @@index([vendorId], map: "idx_stores_vendor_id")
  @@map("stores")
}

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url = ""
}

// Auth models
model User {
  id          BigInt    @id @default(autoincrement())
  phoneNumber String    @unique @map("phone_number") @db.VarChar(20)
  name        String?   @db.VarChar(255)
  email       String?   @db.VarChar(255)
  role        String    @default("customer") @db.VarChar(50)
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  vendors     Vendor[]
  orders      Order[]

  @@map("users")
}

model OtpCode {
  id          BigInt    @id @default(autoincrement())
  phoneNumber String    @map("phone_number") @db.Var<PERSON>har(20)
  code        String    @db.<PERSON>ar<PERSON><PERSON>(6)
  expiresAt   DateTime  @map("expires_at") @db.Timestamptz(6)
  isUsed      Boolean   @default(false) @map("is_used")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  @@index([phoneNumber], map: "idx_otp_codes_phone_number")
  @@index([expiresAt], map: "idx_otp_codes_expires_at")
  @@map("otp_codes")
}

// Vendor models
model Vendor {
  id           BigInt    @id @default(autoincrement())
  userId       BigInt    @map("user_id")
  businessName String    @map("business_name") @db.VarChar(255)
  ownerName    String    @map("owner_name") @db.VarChar(255)
  phoneNumber  String    @map("phone_number") @db.VarChar(20)
  email        String?   @db.VarChar(255)
  address      String    @db.Text
  description  String?   @db.Text
  isActive     Boolean   @default(true) @map("is_active")
  isVerified   Boolean   @default(false) @map("is_verified")
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  user         User      @relation(fields: [userId], references: [id])
  stores       Store[]

  @@index([userId], map: "idx_vendors_user_id")
  @@map("vendors")
}

model Store {
  id             BigInt    @id @default(autoincrement())
  vendorId       BigInt    @map("vendor_id")
  name           String    @db.VarChar(255)
  description    String?   @db.Text
  address        String    @db.Text
  phoneNumber    String?   @map("phone_number") @db.VarChar(20)
  operatingHours Json?     @map("operating_hours")
  isActive       Boolean   @default(true) @map("is_active")
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  vendor         Vendor    @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  products       Product[]

  @@index([vendorId], map: "idx_stores_vendor_id")
  @@map("stores")
}

// Catalog models
model Category {
  id           BigInt        @id @default(autoincrement())
  name         String        @db.VarChar(255)
  description  String?       @db.Text
  icon         String?       @db.VarChar(100)
  isActive     Boolean       @default(true) @map("is_active")
  createdAt    DateTime      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime      @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  subcategories    Subcategory[]
  products         Product[]
  categoryAttributes CategoryAttribute[]

  @@map("categories")
}

model Subcategory {
  id          BigInt    @id @default(autoincrement())
  categoryId  BigInt    @map("category_id")
  name        String    @db.VarChar(255)
  description String?   @db.Text
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  category    Category  @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  products    Product[]

  @@index([categoryId], map: "idx_subcategories_category_id")
  @@map("subcategories")
}

model AttributeDefinition {
  id        BigInt    @id @default(autoincrement())
  name      String    @db.VarChar(255)
  dataType  String    @map("data_type") @db.VarChar(50)
  isRequired Boolean  @default(false) @map("is_required")
  options   Json?
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  categoryAttributes CategoryAttribute[]

  @@map("attribute_definitions")
}

model CategoryAttribute {
  id          BigInt              @id @default(autoincrement())
  categoryId  BigInt              @map("category_id")
  attributeId BigInt              @map("attribute_id")
  isRequired  Boolean             @default(false) @map("is_required")

  // Relations
  category    Category            @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  attribute   AttributeDefinition @relation(fields: [attributeId], references: [id], onDelete: Cascade)

  @@unique([categoryId, attributeId])
  @@index([categoryId], map: "idx_category_attributes_category_id")
  @@map("category_attributes")
}

// Product models
model Product {
  id            BigInt      @id @default(autoincrement())
  storeId       BigInt      @map("store_id")
  categoryId    BigInt      @map("category_id")
  subcategoryId BigInt?     @map("subcategory_id")
  name          String      @db.VarChar(255)
  description   String?     @db.Text
  basePrice     Decimal     @map("base_price") @db.Decimal(10, 2)
  sku           String?     @db.VarChar(100)
  attributes    Json?
  images        Json?
  isActive      Boolean     @default(true) @map("is_active")
  createdAt     DateTime    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime    @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  store         Store       @relation(fields: [storeId], references: [id])
  category      Category    @relation(fields: [categoryId], references: [id])
  subcategory   Subcategory? @relation(fields: [subcategoryId], references: [id])
  inventory     ProductInventory?
  variants      ProductVariant[]
  orderItems    OrderItem[]

  @@index([storeId], map: "idx_products_store_id")
  @@index([categoryId], map: "idx_products_category_id")
  @@index([subcategoryId], map: "idx_products_subcategory_id")
  @@map("products")
}

model ProductInventory {
  id                  BigInt    @id @default(autoincrement())
  productId           BigInt    @unique @map("product_id")
  quantityAvailable   Int       @default(0) @map("quantity_available")
  reservedQuantity    Int       @default(0) @map("reserved_quantity")
  lowStockThreshold   Int       @default(5) @map("low_stock_threshold")
  updatedAt           DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  product             Product   @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId], map: "idx_product_inventory_product_id")
  @@map("product_inventory")
}

model ProductVariant {
  id                BigInt    @id @default(autoincrement())
  productId         BigInt    @map("product_id")
  variantName       String    @map("variant_name") @db.VarChar(255)
  variantAttributes Json      @map("variant_attributes")
  priceAdjustment   Decimal   @default(0) @map("price_adjustment") @db.Decimal(10, 2)
  sku               String?   @db.VarChar(100)
  isActive          Boolean   @default(true) @map("is_active")
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  product           Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems        OrderItem[]

  @@index([productId], map: "idx_product_variants_product_id")
  @@map("product_variants")
}

// Order models
model Order {
  id              BigInt      @id @default(autoincrement())
  userId          BigInt      @map("user_id")
  orderNumber     String      @unique @map("order_number") @db.VarChar(50)
  status          String      @default("pending") @db.VarChar(50)
  totalAmount     Decimal     @map("total_amount") @db.Decimal(10, 2)
  deliveryAddress String      @map("delivery_address") @db.Text
  deliveryPhone   String      @map("delivery_phone") @db.VarChar(20)
  paymentMethod   String      @default("cash_on_delivery") @map("payment_method") @db.VarChar(50)
  notes           String?     @db.Text
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime    @default(now()) @map("updated_at") @db.Timestamptz(6)

  // Relations
  user            User        @relation(fields: [userId], references: [id])
  items           OrderItem[]
  statusHistory   OrderStatusHistory[]

  @@index([userId], map: "idx_orders_user_id")
  @@index([status], map: "idx_orders_status")
  @@index([createdAt], map: "idx_orders_created_at")
  @@map("orders")
}

model OrderItem {
  id               BigInt          @id @default(autoincrement())
  orderId          BigInt          @map("order_id")
  productId        BigInt          @map("product_id")
  productVariantId BigInt?         @map("product_variant_id")
  quantity         Int
  unitPrice        Decimal         @map("unit_price") @db.Decimal(10, 2)
  totalPrice       Decimal         @map("total_price") @db.Decimal(10, 2)
  productSnapshot  Json            @map("product_snapshot")
  createdAt        DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  order            Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product          Product         @relation(fields: [productId], references: [id])
  productVariant   ProductVariant? @relation(fields: [productVariantId], references: [id])

  @@index([orderId], map: "idx_order_items_order_id")
  @@index([productId], map: "idx_order_items_product_id")
  @@map("order_items")
}

model OrderStatusHistory {
  id        BigInt    @id @default(autoincrement())
  orderId   BigInt    @map("order_id")
  status    String    @db.VarChar(50)
  notes     String?   @db.Text
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  order     Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId], map: "idx_order_status_history_order_id")
  @@map("order_status_history")
}

import { api, APIError } from "encore.dev/api";
import { catalogDB } from "./db";

interface CreateSubcategoryRequest {
  categoryId: number;
  name: string;
  description?: string;
}

interface CreateSubcategoryResponse {
  id: number;
  categoryId: number;
  name: string;
  description: string | null;
}

// Creates a new subcategory under an existing category.
export const createSubcategory = api<CreateSubcategoryRequest, CreateSubcategoryResponse>(
  { expose: true, method: "POST", path: "/catalog/subcategories" },
  async (req) => {
    const { categoryId, name, description } = req;

    // Verify category exists
    const category = await catalogDB.queryRow`
      SELECT id FROM categories WHERE id = ${categoryId} AND is_active = true
    `;

    if (!category) {
      throw APIError.notFound("Category not found");
    }

    const result = await catalogDB.queryRow<{ id: number }>`
      INSERT INTO subcategories (category_id, name, description)
      VALUES (${categoryId}, ${name}, ${description || null})
      RETURNING id
    `;

    if (!result) {
      throw new Error("Failed to create subcategory");
    }

    return {
      id: result.id,
      categoryId,
      name,
      description: description || null
    };
  }
);

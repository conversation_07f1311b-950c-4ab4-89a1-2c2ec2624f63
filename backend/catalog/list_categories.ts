import { api } from "encore.dev/api";
import { catalogDB } from "./db";

interface Category {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: number;
  name: string;
  description: string | null;
}

interface ListCategoriesResponse {
  categories: Category[];
}

// Retrieves all active categories with their subcategories.
export const listCategories = api<void, ListCategoriesResponse>(
  { expose: true, method: "GET", path: "/catalog/categories" },
  async () => {
    const categories = await catalogDB.queryAll<{
      id: number;
      name: string;
      description: string | null;
      icon: string | null;
    }>`
      SELECT id, name, description, icon
      FROM categories 
      WHERE is_active = true
      ORDER BY name
    `;

    const categoriesWithSubs: Category[] = [];

    for (const category of categories) {
      const subcategories = await catalogDB.queryAll<Subcategory>`
        SELECT id, name, description
        FROM subcategories 
        WHERE category_id = ${category.id} AND is_active = true
        ORDER BY name
      `;

      categoriesWithSubs.push({
        ...category,
        subcategories
      });
    }

    return { categories: categoriesWithSubs };
  }
);

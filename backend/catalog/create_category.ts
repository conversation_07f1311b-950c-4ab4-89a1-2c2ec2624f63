import { api } from "encore.dev/api";
import { catalogDB } from "./db";

interface CreateCategoryRequest {
  name: string;
  description?: string;
  icon?: string;
}

interface CreateCategoryResponse {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
}

// Creates a new product category.
export const createCategory = api<CreateCategoryRequest, CreateCategoryResponse>(
  { expose: true, method: "POST", path: "/catalog/categories" },
  async (req) => {
    const { name, description, icon } = req;

    const result = await catalogDB.queryRow<{ id: number }>`
      INSERT INTO categories (name, description, icon)
      VALUES (${name}, ${description || null}, ${icon || null})
      RETURNING id
    `;

    if (!result) {
      throw new Error("Failed to create category");
    }

    return {
      id: result.id,
      name,
      description: description || null,
      icon: icon || null
    };
  }
);

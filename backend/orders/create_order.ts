import { api, APIError } from "encore.dev/api";
import { ordersDB } from "./db";

interface OrderItem {
  productId: number;
  productVariantId?: number;
  quantity: number;
}

interface CreateOrderRequest {
  userId: number;
  items: OrderItem[];
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod?: string;
  notes?: string;
}

interface CreateOrderResponse {
  id: number;
  orderNumber: string;
  status: string;
  totalAmount: number;
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod: string;
  notes: string | null;
  items: {
    id: number;
    productId: number;
    productVariantId: number | null;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    productSnapshot: Record<string, any>;
  }[];
}

// Creates a new order with the specified items.
export const createOrder = api<CreateOrderRequest, CreateOrderResponse>(
  { expose: true, method: "POST", path: "/orders" },
  async (req) => {
    const { userId, items, deliveryAddress, deliveryPhone, paymentMethod = "cash_on_delivery", notes } = req;

    if (!items || items.length === 0) {
      throw APIError.invalidArgument("Order must contain at least one item");
    }

    const tx = await ordersDB.begin();

    try {
      // Generate order number
      const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      let totalAmount = 0;
      const orderItemsData: any[] = [];

      // Process each item and calculate total
      for (const item of items) {
        if (item.quantity <= 0) {
          throw APIError.invalidArgument("Item quantity must be greater than 0");
        }

        // Get product details (this would normally check another service)
        // For now, we'll use placeholder data
        const unitPrice = 100; // This should come from product service
        const totalPrice = unitPrice * item.quantity;
        totalAmount += totalPrice;

        const productSnapshot = {
          productId: item.productId,
          productVariantId: item.productVariantId,
          name: `Product ${item.productId}`,
          price: unitPrice
        };

        orderItemsData.push({
          productId: item.productId,
          productVariantId: item.productVariantId || null,
          quantity: item.quantity,
          unitPrice,
          totalPrice,
          productSnapshot
        });
      }

      // Create order
      const orderResult = await tx.queryRow<{ id: number }>`
        INSERT INTO orders (user_id, order_number, total_amount, delivery_address, delivery_phone, payment_method, notes)
        VALUES (${userId}, ${orderNumber}, ${totalAmount}, ${deliveryAddress}, ${deliveryPhone}, ${paymentMethod}, ${notes || null})
        RETURNING id
      `;

      if (!orderResult) {
        throw new Error("Failed to create order");
      }

      const orderId = orderResult.id;

      // Create order items
      const createdItems: any[] = [];
      for (const itemData of orderItemsData) {
        const itemResult = await tx.queryRow<{ id: number }>`
          INSERT INTO order_items (order_id, product_id, product_variant_id, quantity, unit_price, total_price, product_snapshot)
          VALUES (${orderId}, ${itemData.productId}, ${itemData.productVariantId}, ${itemData.quantity}, ${itemData.unitPrice}, ${itemData.totalPrice}, ${JSON.stringify(itemData.productSnapshot)})
          RETURNING id
        `;

        if (itemResult) {
          createdItems.push({
            id: itemResult.id,
            productId: itemData.productId,
            productVariantId: itemData.productVariantId,
            quantity: itemData.quantity,
            unitPrice: itemData.unitPrice,
            totalPrice: itemData.totalPrice,
            productSnapshot: itemData.productSnapshot
          });
        }
      }

      // Create initial status history
      await tx.exec`
        INSERT INTO order_status_history (order_id, status, notes)
        VALUES (${orderId}, 'pending', 'Order created')
      `;

      await tx.commit();

      return {
        id: orderId,
        orderNumber,
        status: "pending",
        totalAmount,
        deliveryAddress,
        deliveryPhone,
        paymentMethod,
        notes: notes || null,
        items: createdItems
      };
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }
);

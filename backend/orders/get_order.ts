import { api, APIError } from "encore.dev/api";
import { ordersDB } from "./db";

interface GetOrderRequest {
  id: number;
}

interface OrderItem {
  id: number;
  productId: number;
  productVariantId: number | null;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  productSnapshot: Record<string, any>;
}

interface StatusHistory {
  id: number;
  status: string;
  notes: string | null;
  createdAt: string;
}

interface GetOrderResponse {
  id: number;
  orderNumber: string;
  status: string;
  totalAmount: number;
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod: string;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  items: OrderItem[];
  statusHistory: StatusHistory[];
}

// Retrieves detailed information about a specific order.
export const getOrder = api<GetOrderRequest, GetOrderResponse>(
  { expose: true, method: "GET", path: "/orders/:id" },
  async (req) => {
    const { id } = req;

    const order = await ordersDB.queryRow<{
      id: number;
      order_number: string;
      status: string;
      total_amount: number;
      delivery_address: string;
      delivery_phone: string;
      payment_method: string;
      notes: string | null;
      created_at: Date;
      updated_at: Date;
    }>`
      SELECT id, order_number, status, total_amount, delivery_address, delivery_phone, payment_method, notes, created_at, updated_at
      FROM orders
      WHERE id = ${id}
    `;

    if (!order) {
      throw APIError.notFound("Order not found");
    }

    // Get order items
    const items = await ordersDB.queryAll<{
      id: number;
      product_id: number;
      product_variant_id: number | null;
      quantity: number;
      unit_price: number;
      total_price: number;
      product_snapshot: any;
    }>`
      SELECT id, product_id, product_variant_id, quantity, unit_price, total_price, product_snapshot
      FROM order_items
      WHERE order_id = ${id}
      ORDER BY id
    `;

    // Get status history
    const statusHistory = await ordersDB.queryAll<{
      id: number;
      status: string;
      notes: string | null;
      created_at: Date;
    }>`
      SELECT id, status, notes, created_at
      FROM order_status_history
      WHERE order_id = ${id}
      ORDER BY created_at DESC
    `;

    return {
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,
      totalAmount: order.total_amount,
      deliveryAddress: order.delivery_address,
      deliveryPhone: order.delivery_phone,
      paymentMethod: order.payment_method,
      notes: order.notes,
      createdAt: order.created_at.toISOString(),
      updatedAt: order.updated_at.toISOString(),
      items: items.map(item => ({
        id: item.id,
        productId: item.product_id,
        productVariantId: item.product_variant_id,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        totalPrice: item.total_price,
        productSnapshot: item.product_snapshot
      })),
      statusHistory: statusHistory.map(sh => ({
        id: sh.id,
        status: sh.status,
        notes: sh.notes,
        createdAt: sh.created_at.toISOString()
      }))
    };
  }
);

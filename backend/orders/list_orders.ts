import { api } from "encore.dev/api";
import { Query } from "encore.dev/api";
import { ordersDB } from "./db";

interface ListOrdersRequest {
  userId?: Query<number>;
  status?: Query<string>;
  limit?: Query<number>;
  offset?: Query<number>;
}

interface Order {
  id: number;
  orderNumber: string;
  status: string;
  totalAmount: number;
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod: string;
  notes: string | null;
  createdAt: string;
  itemCount: number;
}

interface ListOrdersResponse {
  orders: Order[];
  total: number;
}

// Retrieves orders with optional filtering.
export const listOrders = api<ListOrdersRequest, ListOrdersResponse>(
  { expose: true, method: "GET", path: "/orders" },
  async (req) => {
    const { userId, status, limit = 20, offset = 0 } = req;

    let whereConditions = ["1=1"];
    let params: any[] = [];
    let paramIndex = 1;

    if (userId) {
      whereConditions.push(`o.user_id = $${paramIndex}`);
      params.push(userId);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`o.status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    const whereClause = whereConditions.join(" AND ");

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      WHERE ${whereClause}
    `;
    
    const countResult = await ordersDB.rawQueryRow<{ total: number }>(countQuery, ...params);
    const total = countResult?.total || 0;

    // Get orders
    const ordersQuery = `
      SELECT 
        o.id,
        o.order_number,
        o.status,
        o.total_amount,
        o.delivery_address,
        o.delivery_phone,
        o.payment_method,
        o.notes,
        o.created_at,
        COALESCE(oi.item_count, 0) as item_count
      FROM orders o
      LEFT JOIN (
        SELECT order_id, COUNT(*) as item_count
        FROM order_items
        GROUP BY order_id
      ) oi ON o.id = oi.order_id
      WHERE ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(limit, offset);

    const orders = await ordersDB.rawQueryAll<{
      id: number;
      order_number: string;
      status: string;
      total_amount: number;
      delivery_address: string;
      delivery_phone: string;
      payment_method: string;
      notes: string | null;
      created_at: Date;
      item_count: number;
    }>(ordersQuery, ...params);

    return {
      orders: orders.map(o => ({
        id: o.id,
        orderNumber: o.order_number,
        status: o.status,
        totalAmount: o.total_amount,
        deliveryAddress: o.delivery_address,
        deliveryPhone: o.delivery_phone,
        paymentMethod: o.payment_method,
        notes: o.notes,
        createdAt: o.created_at.toISOString(),
        itemCount: o.item_count
      })),
      total
    };
  }
);

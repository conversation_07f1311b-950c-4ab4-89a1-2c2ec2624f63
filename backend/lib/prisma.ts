import { PrismaClient } from '@prisma/client';

// Create a global Prisma client instance
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Export Prisma types for use in other files
export type {
  User,
  Vendor,
  Store,
  Category,
  Subcategory,
  Product,
  ProductInventory,
  ProductVariant,
  Order,
  OrderItem,
  OrderStatusHistory,
  OtpCode,
  AttributeDefinition,
  CategoryAttribute,
} from '@prisma/client';

// Export useful Prisma types
export type {
  Prisma,
} from '@prisma/client';

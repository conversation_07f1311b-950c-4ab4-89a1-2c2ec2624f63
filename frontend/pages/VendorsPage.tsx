import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Store, MapPin, Phone, Mail, Package, CheckCircle } from "lucide-react";
import backend from "~backend/client";

export function VendorsPage() {
  const { data: vendors, isLoading } = useQuery({
    queryKey: ["vendors"],
    queryFn: () => backend.vendors.listVendors(),
  });

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-8 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Local Vendors & Stores</h1>
        <p className="text-gray-600">
          Discover trusted local businesses in your area and shop from verified vendors.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {vendors?.vendors.map((vendor) => (
          <Card key={vendor.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Store className="w-5 h-5 text-blue-600" />
                  <div>
                    <h3 className="text-lg font-semibold">{vendor.businessName}</h3>
                    <p className="text-sm text-gray-600 font-normal">{vendor.ownerName}</p>
                  </div>
                </div>
                {vendor.isVerified && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {vendor.description && (
                <p className="text-sm text-gray-700 line-clamp-2">
                  {vendor.description}
                </p>
              )}

              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2 text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span className="line-clamp-1">{vendor.address}</span>
                </div>
                
                <div className="flex items-center space-x-2 text-gray-600">
                  <Phone className="w-4 h-4" />
                  <span>{vendor.phoneNumber}</span>
                </div>
                
                {vendor.email && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Mail className="w-4 h-4" />
                    <span className="line-clamp-1">{vendor.email}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Package className="w-4 h-4" />
                  <span>{vendor.storeCount} store{vendor.storeCount !== 1 ? 's' : ''}</span>
                </div>
                
                <Button asChild size="sm">
                  <Link to={`/products?storeId=${vendor.id}`}>
                    View Products
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {vendors?.vendors.length === 0 && (
        <div className="text-center py-12">
          <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No vendors found</h3>
          <p className="text-gray-500">Check back later as we're constantly adding new local businesses.</p>
        </div>
      )}
    </div>
  );
}

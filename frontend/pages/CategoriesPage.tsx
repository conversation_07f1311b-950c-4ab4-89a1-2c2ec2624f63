import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import backend from "~backend/client";

export function CategoriesPage() {
  const { data: categories, isLoading } = useQuery({
    queryKey: ["categories"],
    queryFn: () => backend.catalog.listCategories(),
  });

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <Skeleton key={j} className="h-4 w-24" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Product Categories</h1>
        <p className="text-gray-600">
          Browse our wide range of product categories and find exactly what you need.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories?.categories.map((category) => (
          <Card key={category.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <span className="text-2xl">{category.icon || "📦"}</span>
                <div>
                  <h3 className="text-lg font-semibold">{category.name}</h3>
                  {category.description && (
                    <p className="text-sm text-gray-600 font-normal">
                      {category.description}
                    </p>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Subcategories:</span>
                  <Badge variant="secondary">
                    {category.subcategories.length}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  {category.subcategories.slice(0, 5).map((subcategory) => (
                    <Link
                      key={subcategory.id}
                      to={`/products?categoryId=${category.id}&subcategoryId=${subcategory.id}`}
                      className="block text-sm text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {subcategory.name}
                    </Link>
                  ))}
                  {category.subcategories.length > 5 && (
                    <Link
                      to={`/products?categoryId=${category.id}`}
                      className="block text-sm text-gray-500 hover:text-gray-700"
                    >
                      +{category.subcategories.length - 5} more...
                    </Link>
                  )}
                </div>

                <div className="pt-3">
                  <Link
                    to={`/products?categoryId=${category.id}`}
                    className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
                  >
                    View all products →
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {categories?.categories.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No categories available at the moment.</p>
        </div>
      )}
    </div>
  );
}

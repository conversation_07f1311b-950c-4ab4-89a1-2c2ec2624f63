import { useState } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, ShoppingCart, Store, Package, Minus, Plus } from "lucide-react";
import { useCart } from "../contexts/CartContext";
import { useToast } from "@/hooks/use-toast";
import backend from "~backend/client";

export function ProductDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);
  const { addItem } = useCart();
  const { toast } = useToast();

  const { data: product, isLoading } = useQuery({
    queryKey: ["product", id],
    queryFn: () => backend.products.getProduct({ id: parseInt(id!) }),
    enabled: !!id,
  });

  const handleAddToCart = () => {
    if (!product) return;

    const variant = selectedVariant ? product.variants.find(v => v.id === selectedVariant) : null;
    const price = product.basePrice + (variant?.priceAdjustment || 0);

    for (let i = 0; i < quantity; i++) {
      addItem({
        productId: product.id,
        productVariantId: selectedVariant || undefined,
        name: variant ? `${product.name} (${variant.variantName})` : product.name,
        price,
        image: product.images?.[0],
      });
    }

    toast({
      title: "Added to cart",
      description: `${quantity}x ${product.name} has been added to your cart.`,
    });
  };

  const currentPrice = product ? product.basePrice + (selectedVariant ? product.variants.find(v => v.id === selectedVariant)?.priceAdjustment || 0 : 0) : 0;

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Skeleton className="h-6 w-32 mb-6" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Skeleton className="aspect-square" />
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-6 w-1/4" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Product not found</h3>
          <p className="text-gray-500 mb-4">The product you're looking for doesn't exist.</p>
          <Button asChild>
            <Link to="/products">Browse Products</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/products">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Products
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
            {product.images && product.images.length > 0 ? (
              <img
                src={product.images[0]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Package className="w-24 h-24 text-gray-400" />
            )}
          </div>
          
          {product.images && product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.slice(1, 5).map((image, index) => (
                <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt={`${product.name} ${index + 2}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
            <div className="flex items-center space-x-2 mb-4">
              <Link to={`/products?storeId=${product.storeId}`} className="flex items-center text-blue-600 hover:text-blue-800">
                <Store className="w-4 h-4 mr-1" />
                {product.storeName}
              </Link>
              <span className="text-gray-400">•</span>
              <Link to={`/products?categoryId=${product.categoryId}`} className="text-gray-600 hover:text-gray-800">
                {product.categoryName}
              </Link>
              {product.subcategoryName && (
                <>
                  <span className="text-gray-400">•</span>
                  <Link to={`/products?subcategoryId=${product.subcategoryId}`} className="text-gray-600 hover:text-gray-800">
                    {product.subcategoryName}
                  </Link>
                </>
              )}
            </div>
            
            <div className="flex items-center space-x-4 mb-4">
              <span className="text-3xl font-bold text-blue-600">₹{currentPrice}</span>
              <Badge variant={product.quantityAvailable > 0 ? "default" : "secondary"}>
                {product.quantityAvailable > 0 ? `${product.quantityAvailable} in stock` : "Out of stock"}
              </Badge>
            </div>

            {product.sku && (
              <p className="text-sm text-gray-500 mb-4">SKU: {product.sku}</p>
            )}
          </div>

          {product.description && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Description</h3>
              <p className="text-gray-700">{product.description}</p>
            </div>
          )}

          {/* Product Variants */}
          {product.variants.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Variants</h3>
              <div className="grid grid-cols-2 gap-2">
                {product.variants.map((variant) => (
                  <Button
                    key={variant.id}
                    variant={selectedVariant === variant.id ? "default" : "outline"}
                    onClick={() => setSelectedVariant(selectedVariant === variant.id ? null : variant.id)}
                    className="justify-start"
                  >
                    <div className="text-left">
                      <div className="font-medium">{variant.variantName}</div>
                      {variant.priceAdjustment !== 0 && (
                        <div className="text-xs">
                          {variant.priceAdjustment > 0 ? '+' : ''}₹{variant.priceAdjustment}
                        </div>
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Product Attributes */}
          {product.attributes && Object.keys(product.attributes).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Specifications</h3>
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-2">
                    {Object.entries(product.attributes).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                        <span className="text-gray-700">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <Separator />

          {/* Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <span className="font-medium">Quantity:</span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  <Minus className="w-4 h-4" />
                </Button>
                <span className="w-12 text-center">{quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(Math.min(product.quantityAvailable, quantity + 1))}
                  disabled={quantity >= product.quantityAvailable}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <Button
              onClick={handleAddToCart}
              disabled={product.quantityAvailable === 0}
              className="w-full"
              size="lg"
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              Add to Cart - ₹{currentPrice * quantity}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

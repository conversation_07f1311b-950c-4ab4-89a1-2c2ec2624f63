import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import backend from "~backend/client";
import { useCart } from "../contexts/CartContext";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from "./use-toast";

interface CreateOrderData {
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod?: string;
  notes?: string;
}

export function useCreateOrder() {
  const { items, clearCart } = useCart();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: CreateOrderData) => {
      if (!user) {
        throw new Error("User must be logged in to create an order");
      }

      if (items.length === 0) {
        throw new Error("Cart is empty");
      }

      const orderItems = items.map(item => ({
        productId: item.productId,
        productVariantId: item.productVariantId,
        quantity: item.quantity,
      }));

      return backend.orders.createOrder({
        userId: user.id,
        items: orderItems,
        deliveryAddress: orderData.deliveryAddress,
        deliveryPhone: orderData.deliveryPhone,
        paymentMethod: orderData.paymentMethod || "cash_on_delivery",
        notes: orderData.notes,
      });
    },
    onSuccess: (order) => {
      // Clear the cart
      clearCart();
      
      // Invalidate orders query to refresh the orders list
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      
      // Show success message
      toast({
        title: "Order placed successfully!",
        description: `Order #${order.orderNumber} has been created.`,
      });
      
      // Navigate to order details page
      navigate(`/orders/${order.id}`);
    },
    onError: (error: any) => {
      console.error("Failed to create order:", error);
      toast({
        title: "Failed to place order",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive",
      });
    },
  });
}

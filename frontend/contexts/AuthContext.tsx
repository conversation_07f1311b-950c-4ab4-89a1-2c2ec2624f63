import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import backend from "~backend/client";

interface User {
  id: number;
  phoneNumber: string;
  name: string | null;
  email: string | null;
  role: string;
}

interface VerifyOTPResponse {
  success: boolean;
  user: User;
  token: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  login: (phoneNumber: string, otpCode: string) => Promise<void>;
  logout: () => void;
  sendOTP: (phoneNumber: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored auth data on mount
    const storedUser = localStorage.getItem("user");
    const storedToken = localStorage.getItem("token");

    if (storedUser && storedToken) {
      setUser(JSON.parse(storedUser));
      setToken(storedToken);
    }

    setIsLoading(false);
  }, []);

  const sendOTP = async (phoneNumber: string) => {
    try {
      await backend.auth.sendOTP({ phoneNumber });
    } catch (error) {
      console.error("Failed to send OTP:", error);
      throw error;
    }
  };

  const login = async (phoneNumber: string, otpCode: string) => {
    try {
      const response: VerifyOTPResponse = await backend.auth.verifyOTP({
        phoneNumber,
        otpCode,
      });

      if (response.success) {
        setUser(response.user);
        setToken(response.token);

        // Store in localStorage
        localStorage.setItem("user", JSON.stringify(response.user));
        localStorage.setItem("token", response.token);
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem("user");
    localStorage.removeItem("token");
  };

  const value = {
    user,
    token,
    isLoading,
    login,
    logout,
    sendOTP,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

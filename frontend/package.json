{"name": "frontend", "version": "1.0.0", "type": "module", "packageManager": "bun", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.77.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.484.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@tailwindcss/oxide": "^4.1.7", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "lightningcss": "^1.30.1", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "optionalDependencies": {"rollup": "^4.41.1"}}
import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Plus, Edit, Trash2, Save, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import backend from "~backend/client";

interface ProductVariant {
  id: number;
  variantName: string;
  variantAttributes: Record<string, any>;
  priceAdjustment: number;
  sku: string | null;
  isActive: boolean;
  createdAt: string;
}

interface VariantManagerProps {
  productId: number;
}

interface VariantFormData {
  variantName: string;
  variantAttributes: Record<string, string>;
  priceAdjustment: number;
  sku: string;
}

export function VariantManager({ productId }: VariantManagerProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState<VariantFormData>({
    variantName: "",
    variantAttributes: {},
    priceAdjustment: 0,
    sku: "",
  });
  const [attributeKey, setAttributeKey] = useState("");
  const [attributeValue, setAttributeValue] = useState("");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch variants
  const { data: variants, isLoading } = useQuery({
    queryKey: ["variants", productId],
    queryFn: () => backend.products.listVariants({ productId }),
  });

  // Create variant mutation
  const createVariantMutation = useMutation({
    mutationFn: (data: VariantFormData) =>
      backend.products.createVariant({
        productId,
        variantName: data.variantName,
        variantAttributes: data.variantAttributes,
        priceAdjustment: data.priceAdjustment,
        sku: data.sku || undefined,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["variants", productId] });
      queryClient.invalidateQueries({ queryKey: ["product", productId.toString()] });
      setIsCreating(false);
      resetForm();
      toast({ title: "Variant created successfully!" });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to create variant",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update variant mutation
  const updateVariantMutation = useMutation({
    mutationFn: ({ variantId, data }: { variantId: number; data: Partial<VariantFormData> }) =>
      backend.products.updateVariant({
        productId,
        variantId,
        ...data,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["variants", productId] });
      queryClient.invalidateQueries({ queryKey: ["product", productId.toString()] });
      setEditingId(null);
      resetForm();
      toast({ title: "Variant updated successfully!" });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to update variant",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete variant mutation
  const deleteVariantMutation = useMutation({
    mutationFn: (variantId: number) =>
      backend.products.deleteVariant({ productId, variantId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["variants", productId] });
      queryClient.invalidateQueries({ queryKey: ["product", productId.toString()] });
      toast({ title: "Variant deleted successfully!" });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to delete variant",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      variantName: "",
      variantAttributes: {},
      priceAdjustment: 0,
      sku: "",
    });
    setAttributeKey("");
    setAttributeValue("");
  };

  const addAttribute = () => {
    if (attributeKey && attributeValue) {
      setFormData(prev => ({
        ...prev,
        variantAttributes: {
          ...prev.variantAttributes,
          [attributeKey]: attributeValue,
        },
      }));
      setAttributeKey("");
      setAttributeValue("");
    }
  };

  const removeAttribute = (key: string) => {
    setFormData(prev => ({
      ...prev,
      variantAttributes: Object.fromEntries(
        Object.entries(prev.variantAttributes).filter(([k]) => k !== key)
      ),
    }));
  };

  const startEdit = (variant: ProductVariant) => {
    setEditingId(variant.id);
    setFormData({
      variantName: variant.variantName,
      variantAttributes: variant.variantAttributes,
      priceAdjustment: variant.priceAdjustment,
      sku: variant.sku || "",
    });
  };

  const handleSubmit = () => {
    if (editingId) {
      updateVariantMutation.mutate({ variantId: editingId, data: formData });
    } else {
      createVariantMutation.mutate(formData);
    }
  };

  if (isLoading) {
    return <div>Loading variants...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Product Variants
          <Button
            onClick={() => setIsCreating(true)}
            disabled={isCreating || editingId !== null}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Variant
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Create/Edit Form */}
        {(isCreating || editingId !== null) && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {editingId ? "Edit Variant" : "Create New Variant"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="variantName">Variant Name</Label>
                <Input
                  id="variantName"
                  value={formData.variantName}
                  onChange={(e) => setFormData(prev => ({ ...prev, variantName: e.target.value }))}
                  placeholder="e.g., 256GB Storage"
                />
              </div>

              <div>
                <Label htmlFor="priceAdjustment">Price Adjustment (₹)</Label>
                <Input
                  id="priceAdjustment"
                  type="number"
                  step="0.01"
                  value={formData.priceAdjustment}
                  onChange={(e) => setFormData(prev => ({ ...prev, priceAdjustment: parseFloat(e.target.value) || 0 }))}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="sku">SKU (Optional)</Label>
                <Input
                  id="sku"
                  value={formData.sku}
                  onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
                  placeholder="e.g., IP15P-256"
                />
              </div>

              <div>
                <Label>Attributes</Label>
                <div className="space-y-2">
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Attribute name"
                      value={attributeKey}
                      onChange={(e) => setAttributeKey(e.target.value)}
                    />
                    <Input
                      placeholder="Attribute value"
                      value={attributeValue}
                      onChange={(e) => setAttributeValue(e.target.value)}
                    />
                    <Button onClick={addAttribute} disabled={!attributeKey || !attributeValue}>
                      Add
                    </Button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(formData.variantAttributes).map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="flex items-center space-x-1">
                        <span>{key}: {value}</span>
                        <button
                          onClick={() => removeAttribute(key)}
                          className="ml-1 text-red-500 hover:text-red-700"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={handleSubmit}
                  disabled={!formData.variantName || createVariantMutation.isPending || updateVariantMutation.isPending}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {editingId ? "Update" : "Create"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsCreating(false);
                    setEditingId(null);
                    resetForm();
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <Separator />

        {/* Variants List */}
        <div className="space-y-3">
          {variants?.variants.map((variant) => (
            <Card key={variant.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{variant.variantName}</h4>
                      <Badge variant={variant.isActive ? "default" : "secondary"}>
                        {variant.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      Price adjustment: {variant.priceAdjustment >= 0 ? "+" : ""}₹{variant.priceAdjustment}
                    </p>
                    {variant.sku && (
                      <p className="text-sm text-gray-500">SKU: {variant.sku}</p>
                    )}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {Object.entries(variant.variantAttributes).map(([key, value]) => (
                        <Badge key={key} variant="outline" className="text-xs">
                          {key}: {String(value)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => startEdit(variant)}
                      disabled={isCreating || editingId !== null}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteVariantMutation.mutate(variant.id)}
                      disabled={deleteVariantMutation.isPending}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {variants?.variants.length === 0 && (
            <p className="text-center text-gray-500 py-8">
              No variants found. Create your first variant above.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

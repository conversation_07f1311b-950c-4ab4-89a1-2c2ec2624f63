import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";

interface CheckoutFormData {
  deliveryAddress: string;
  deliveryPhone: string;
  paymentMethod: string;
  notes: string;
}

interface CheckoutFormProps {
  onSubmit: (data: CheckoutFormData) => void;
  isLoading?: boolean;
}

export function CheckoutForm({ onSubmit, isLoading = false }: CheckoutFormProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState<CheckoutFormData>({
    deliveryAddress: "",
    deliveryPhone: user?.phoneNumber || "",
    paymentMethod: "cash_on_delivery",
    notes: "",
  });

  const [errors, setErrors] = useState<Partial<CheckoutFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<CheckoutFormData> = {};

    if (!formData.deliveryAddress.trim()) {
      newErrors.deliveryAddress = "Delivery address is required";
    }

    if (!formData.deliveryPhone.trim()) {
      newErrors.deliveryPhone = "Phone number is required";
    } else if (!/^\+?[\d\s-()]+$/.test(formData.deliveryPhone)) {
      newErrors.deliveryPhone = "Please enter a valid phone number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: keyof CheckoutFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Delivery Details</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Delivery Address */}
          <div>
            <Label htmlFor="deliveryAddress">Delivery Address *</Label>
            <Input
              id="deliveryAddress"
              type="text"
              placeholder="Enter your complete delivery address"
              value={formData.deliveryAddress}
              onChange={(e) => handleInputChange("deliveryAddress", e.target.value)}
              className={errors.deliveryAddress ? "border-red-500" : ""}
            />
            {errors.deliveryAddress && (
              <p className="text-sm text-red-500 mt-1">{errors.deliveryAddress}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <Label htmlFor="deliveryPhone">Phone Number *</Label>
            <Input
              id="deliveryPhone"
              type="tel"
              placeholder="Enter your phone number"
              value={formData.deliveryPhone}
              onChange={(e) => handleInputChange("deliveryPhone", e.target.value)}
              className={errors.deliveryPhone ? "border-red-500" : ""}
            />
            {errors.deliveryPhone && (
              <p className="text-sm text-red-500 mt-1">{errors.deliveryPhone}</p>
            )}
          </div>

          {/* Payment Method */}
          <div>
            <Label htmlFor="paymentMethod">Payment Method</Label>
            <Select
              value={formData.paymentMethod}
              onValueChange={(value) => handleInputChange("paymentMethod", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cash_on_delivery">Cash on Delivery</SelectItem>
                <SelectItem value="online" disabled>
                  Online Payment (Coming Soon)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Special Instructions (Optional)</Label>
            <Input
              id="notes"
              type="text"
              placeholder="Any special delivery instructions..."
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Placing Order...
              </>
            ) : (
              "Place Order"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
